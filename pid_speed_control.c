#include <stdio.h>
#include <stdlib.h>
#include <math.h>

// PID参数结构体
typedef struct {
    float kp;        // 比例系数
    float ki;        // 积分系数  
    float kd;        // 微分系数
    float prev_error; // 上次误差
    float integral;   // 积分累积
    float max_output; // 输出限幅
    float min_output; // 输出下限
} PID_Controller;

// 初始化PID控制器
void pid_init(PID_Controller *pid, float kp, float ki, float kd, float max_out, float min_out) {
    pid->kp = kp;
    pid->ki = ki;
    pid->kd = kd;
    pid->prev_error = 0.0;
    pid->integral = 0.0;
    pid->max_output = max_out;
    pid->min_output = min_out;
}

// PID计算函数
float pid_compute(PID_Controller *pid, float setpoint, float current_value, float dt) {
    float error = setpoint - current_value;           // 计算误差
    pid->integral += error * dt;                      // 积分项累积
    float derivative = (error - pid->prev_error) / dt; // 微分项计算
    
    // PID输出计算
    float output = pid->kp * error + pid->ki * pid->integral + pid->kd * derivative;
    
    // 输出限幅
    if (output > pid->max_output) output = pid->max_output;
    if (output < pid->min_output) output = pid->min_output;
    
    // 积分饱和处理
    if (output >= pid->max_output || output <= pid->min_output) {
        pid->integral -= error * dt; // 回退积分
    }
    
    pid->prev_error = error; // 保存当前误差
    return output;
}

// 模拟电机响应函数
float motor_response(float control_signal, float current_speed) {
    float motor_gain = 0.8;      // 电机增益
    float motor_inertia = 0.95;  // 电机惯性
    return current_speed * motor_inertia + control_signal * motor_gain;
}

// 主函数演示PID调速
int main() {
    PID_Controller speed_pid;
    pid_init(&speed_pid, 2.0, 0.5, 0.1, 100.0, -100.0); // 初始化PID参数
    
    float target_speed = 50.0;  // 目标转速
    float current_speed = 0.0;  // 当前转速
    float dt = 0.01;            // 采样时间(10ms)
    float control_output;       // 控制输出
    
    printf("PID速度控制仿真\n");
    printf("目标速度: %.1f RPM\n", target_speed);
    printf("时间(s)\t当前速度\t误差\t\t控制输出\n");
    
    // 仿真运行200个周期
    for (int i = 0; i < 200; i++) {
        float time = i * dt;
        
        // PID控制计算
        control_output = pid_compute(&speed_pid, target_speed, current_speed, dt);
        
        // 电机响应模拟
        current_speed = motor_response(control_output, current_speed);
        
        // 每10个周期输出一次数据
        if (i % 10 == 0) {
            float error = target_speed - current_speed;
            printf("%.2f\t\t%.2f\t\t%.2f\t\t%.2f\n", time, current_speed, error, control_output);
        }
        
        // 在1秒时改变目标速度测试响应
        if (i == 100) {
            target_speed = 80.0;
            printf("\n--- 目标速度改变为 %.1f RPM ---\n", target_speed);
        }
    }
    
    return 0;
}
